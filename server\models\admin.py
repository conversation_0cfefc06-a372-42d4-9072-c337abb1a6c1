"""
管理员模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Enum
from sqlalchemy.sql import func
from .base import Base
import enum


class AdminRole(enum.Enum):
    """管理员角色枚举"""
    admin = "admin"
    super_admin = "super_admin"


class Admin(Base):
    """管理员表模型"""
    __tablename__ = "admins"

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(Enum(AdminRole), default=AdminRole.admin)
    created_at = Column(DateTime, default=func.now())

    def __repr__(self):
        return f"<Admin(id={self.id}, username='{self.username}', role='{self.role.value}')>"
