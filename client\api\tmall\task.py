from curl_cffi import requests

base_url = "http://ecommerce-upms-proxy.dataduoduo.com"



def pull_task(token, platform, jobType):
    url = f"{base_url}/api/pullTask"
    headers = {
        "token": token,
        "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "ecommerce-upms-proxy.dataduoduo.com",
        "Connection": "keep-alive",
    }
    data = {"platform": platform, "jobType": jobType}

    response = requests.post(url, headers=headers, json=data, timeout=10)

    return response.json()


def upload_result(token, traceId, result, item, jobType, platform):
    url = f"{base_url}/api/taskResultUpload"
    headers = {
        "token": token,
        "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "ecommerce-upms-proxy.dataduoduo.com",
        "Connection": "keep-alive",
    }
    data = {
        "traceId": traceId,
        "result": result,
        "item": item,
        "jobType": jobType,
        "platform": platform,
    }

    response = requests.post(url, headers=headers, json=data, timeout=10)

    return response.json()

if __name__ == "__main__":
    result = pull_task(token="b2bbca5739854bba97b81f422a6ba11c",platform='tmall',jobType="BasicInfo")
    print(result)
