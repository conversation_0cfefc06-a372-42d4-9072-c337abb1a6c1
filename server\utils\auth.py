"""
认证工具模块
提供密码加密、JWT token生成和验证等功能
"""
import os
import bcrypt
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from fastapi import HTTPException, status, Depends
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from database import get_db_session
from models import User

# JWT配置
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

# HTTP Bearer认证方案
security = HTTPBearer()


def hash_password(password: str) -> str:
    """
    使用bcrypt加密密码
    
    Args:
        password: 明文密码
        
    Returns:
        str: 加密后的密码哈希
    """
    # 生成盐并加密密码
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码是否正确
    
    Args:
        plain_password: 明文密码
        hashed_password: 加密后的密码哈希
        
    Returns:
        bool: 密码是否正确
    """
    try:
        return bcrypt.checkpw(
            plain_password.encode('utf-8'), 
            hashed_password.encode('utf-8')
        )
    except Exception:
        return False


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    创建JWT访问令牌
    
    Args:
        data: 要编码到token中的数据
        expires_delta: token过期时间，默认为配置的过期时间
        
    Returns:
        str: JWT token字符串
    """
    to_encode = data.copy()
    
    # 设置过期时间
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    
    # 生成JWT token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    验证JWT token并返回payload
    
    Args:
        token: JWT token字符串
        
    Returns:
        Optional[Dict[str, Any]]: token payload，验证失败返回None
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None


def get_current_user_from_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db_session)
) -> User:
    """
    从JWT token获取当前用户
    
    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 验证token
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
        
        # 获取用户ID
        user_id: int = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except (JWTError, ValueError):
        raise credentials_exception
    
    # 从数据库获取用户
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception
    
    # 检查用户状态
    if user.status.value != 'active':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被禁用"
        )
    
    return user


def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db_session)
) -> Optional[User]:
    """
    可选的用户认证，不强制要求登录
    
    Args:
        credentials: HTTP Bearer认证凭据（可选）
        db: 数据库会话
        
    Returns:
        Optional[User]: 当前用户对象，未登录时返回None
    """
    if not credentials:
        return None
    
    try:
        return get_current_user_from_token(credentials, db)
    except HTTPException:
        return None


def authenticate_user(username: str, password: str, db: Session) -> Optional[User]:
    """
    验证用户名和密码
    
    Args:
        username: 用户名
        password: 密码
        db: 数据库会话
        
    Returns:
        Optional[User]: 验证成功返回用户对象，失败返回None
    """
    # 查找用户
    user = db.query(User).filter(User.username == username).first()
    if not user:
        return None
    
    # 验证密码
    if not verify_password(password, user.password_hash):
        return None
    
    # 检查用户状态
    if user.status.value != 'active':
        return None
    
    return user


def create_user_token(user: User) -> str:
    """
    为用户创建访问令牌
    
    Args:
        user: 用户对象
        
    Returns:
        str: JWT token字符串
    """
    token_data = {
        "sub": user.id,
        "username": user.username,
        "iat": datetime.now(timezone.utc)
    }
    return create_access_token(token_data)
