"""
认证中间件
提供JWT token验证和用户认证功能
"""
from typing import Optional, Callable
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from database import get_db_session
from utils.auth import verify_token, get_current_user_from_token
from models import User

# HTTP Bearer认证方案
security = HTTPBearer(auto_error=False)


class AuthMiddleware:
    """认证中间件类"""
    
    def __init__(self, require_auth: bool = True):
        """
        初始化认证中间件
        
        Args:
            require_auth: 是否强制要求认证，False时允许匿名访问
        """
        self.require_auth = require_auth
    
    def __call__(self, 
                 credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
                 db: Session = Depends(get_db_session)) -> Optional[User]:
        """
        中间件调用方法
        
        Args:
            credentials: HTTP Bearer认证凭据
            db: 数据库会话
            
        Returns:
            Optional[User]: 当前用户对象，未认证时根据require_auth决定返回None或抛出异常
            
        Raises:
            HTTPException: 认证失败且require_auth=True时抛出401错误
        """
        if not credentials:
            if self.require_auth:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要认证",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return None
        
        try:
            # 验证token
            payload = verify_token(credentials.credentials)
            if payload is None:
                if self.require_auth:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="无效的认证凭据",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                return None
            
            # 获取用户ID
            user_id: int = payload.get("sub")
            if user_id is None:
                if self.require_auth:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="无效的token格式",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                return None
            
            # 从数据库获取用户
            user = db.query(User).filter(User.id == user_id).first()
            if user is None:
                if self.require_auth:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="用户不存在",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                return None
            
            # 检查用户状态
            if user.status.value != 'active':
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="账户已被禁用"
                )
            
            return user
            
        except HTTPException:
            raise
        except Exception:
            if self.require_auth:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="认证失败",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return None


# 预定义的中间件实例
require_auth = AuthMiddleware(require_auth=True)  # 强制要求认证
optional_auth = AuthMiddleware(require_auth=False)  # 可选认证


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db_session)
) -> User:
    """
    获取当前认证用户（强制要求认证）
    
    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    return get_current_user_from_token(credentials, db)


def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db_session)
) -> Optional[User]:
    """
    获取当前用户（可选认证）
    
    Args:
        credentials: HTTP Bearer认证凭据（可选）
        db: 数据库会话
        
    Returns:
        Optional[User]: 当前用户对象，未认证时返回None
    """
    if not credentials:
        return None
    
    try:
        return get_current_user_from_token(credentials, db)
    except HTTPException:
        return None


def admin_required(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    要求管理员权限的依赖
    
    Args:
        current_user: 当前用户
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 非管理员用户时抛出403错误
    """
    # 注意：这里假设User模型有is_admin字段或类似的权限检查
    # 实际实现需要根据具体的权限模型调整
    if not hasattr(current_user, 'is_admin') or not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user
