"""
任务相关模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum, Boolean, JSON, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base
import enum


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    pending = "pending"
    assigned = "assigned"
    in_progress = "in_progress"
    completed = "completed"
    failed = "failed"
    timeout = "timeout"


class TaskImportStatus(enum.Enum):
    """任务导入状态枚举"""
    processing = "processing"
    completed = "completed"
    failed = "failed"


class FileType(enum.Enum):
    """文件类型枚举"""
    excel = "excel"
    csv = "csv"
    json = "json"


class Task(Base):
    """任务表模型"""
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    item_id = Column(String(100), nullable=False)  # 商品ID
    platform_id = Column(Integer, ForeignKey("platforms.id"), nullable=False)
    task_type = Column(String(50), default="BasicInfo")
    status = Column(Enum(TaskStatus), default=TaskStatus.pending)
    assigned_to = Column(Integer, ForeignKey("users.id"))  # 分配给的用户
    assigned_at = Column(DateTime)
    completed_at = Column(DateTime)
    import_batch_id = Column(String(50))  # 批量导入批次ID
    created_at = Column(DateTime, default=func.now())

    # 关系定义
    platform = relationship("Platform", back_populates="tasks")
    assigned_user = relationship("User", back_populates="tasks")
    results = relationship("TaskResult", back_populates="task")

    def __repr__(self):
        return f"<Task(id={self.id}, item_id='{self.item_id}', status='{self.status.value}')>"


class TaskResult(Base):
    """任务结果表模型"""
    __tablename__ = "task_results"

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    result_data = Column(JSON)  # 爬取的商品数据
    success = Column(Boolean, nullable=False)
    error_message = Column(Text)
    submitted_at = Column(DateTime, default=func.now())

    # 关系定义
    task = relationship("Task", back_populates="results")
    user = relationship("User")

    def __repr__(self):
        return f"<TaskResult(id={self.id}, task_id={self.task_id}, success={self.success})>"


class TaskImport(Base):
    """任务导入记录表模型"""
    __tablename__ = "task_imports"

    id = Column(Integer, primary_key=True, autoincrement=True)
    batch_id = Column(String(50), unique=True, nullable=False)
    filename = Column(String(255), nullable=False)
    file_type = Column(Enum(FileType), nullable=False)
    total_count = Column(Integer, nullable=False)  # 总任务数
    success_count = Column(Integer, default=0)  # 成功导入数
    failed_count = Column(Integer, default=0)  # 失败数
    status = Column(Enum(TaskImportStatus), default=TaskImportStatus.processing)
    error_details = Column(JSON)  # 错误详情
    created_by = Column(Integer, ForeignKey("admins.id"), nullable=False)  # 操作管理员
    created_at = Column(DateTime, default=func.now())

    # 关系定义
    admin = relationship("Admin")

    def __repr__(self):
        return f"<TaskImport(id={self.id}, batch_id='{self.batch_id}', status='{self.status.value}')>"
