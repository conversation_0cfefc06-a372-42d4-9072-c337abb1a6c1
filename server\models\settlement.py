"""
结算模型
"""
from sqlalchemy import Column, Integer, DateTime, ForeignKey, Enum, DECIMAL, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base
import enum


class SettlementStatus(enum.Enum):
    """结算状态枚举"""
    pending = "pending"
    completed = "completed"


class Settlement(Base):
    """结算记录表模型"""
    __tablename__ = "settlements"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount = Column(DECIMAL(10, 2), nullable=False)  # 结算金额
    task_count = Column(Integer, nullable=False)  # 结算时的任务数量
    referral_task_count = Column(Integer, default=0)  # 结算时的被邀请人任务数量
    settlement_period_start = Column(DateTime)  # 结算周期开始时间
    settlement_period_end = Column(DateTime)  # 结算周期结束时间
    status = Column(Enum(SettlementStatus), default=SettlementStatus.pending)
    remark = Column(Text)  # 结算备注
    created_at = Column(DateTime, default=func.now())
    completed_at = Column(DateTime)

    # 关系定义
    user = relationship("User", back_populates="settlements")

    def __repr__(self):
        return f"<Settlement(id={self.id}, user_id={self.user_id}, amount={self.amount}, status='{self.status.value}')>"
