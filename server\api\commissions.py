"""
佣金相关API接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging

from database import get_db_session
from models import User
from services import CommissionService
from pydantic import BaseModel

logger = logging.getLogger(__name__)
router = APIRouter()


class CommissionBalanceResponse(BaseModel):
    """佣金余额响应"""
    total_earnings: float
    task_earnings: float
    referral_earnings: float
    settled_amount: float
    available_balance: float
    task_count: int
    referral_task_count: int


class PlatformCommissionDetail(BaseModel):
    """平台佣金详情"""
    platform_code: str
    platform_name: str
    base_price: float
    commission_rate: float
    task_count: int
    referral_task_count: int
    task_earnings: float
    referral_earnings: float
    total_earnings: float


class CommissionDetailResponse(BaseModel):
    """佣金详情响应"""
    balance: CommissionBalanceResponse
    platform_details: List[PlatformCommissionDetail]


@router.get("/balance/{user_id}", response_model=CommissionBalanceResponse)
async def get_commission_balance(
    user_id: int,
    db: Session = Depends(get_db_session)
):
    """
    获取用户佣金余额
    
    Args:
        user_id: 用户ID
        db: 数据库会话
        
    Returns:
        CommissionBalanceResponse: 佣金余额信息
    """
    try:
        # 验证用户存在
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 计算佣金
        commission_service = CommissionService(db)
        commission_data = commission_service.calculate_user_commission(user_id)
        
        return CommissionBalanceResponse(
            total_earnings=float(commission_data['total_earnings']),
            task_earnings=float(commission_data['task_earnings']),
            referral_earnings=float(commission_data['referral_earnings']),
            settled_amount=float(commission_data['settled_amount']),
            available_balance=float(commission_data['available_balance']),
            task_count=commission_data['task_count'],
            referral_task_count=commission_data['referral_task_count']
        )
        
    except Exception as e:
        logger.error(f"获取用户{user_id}佣金余额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取佣金余额失败"
        )


@router.get("/details/{user_id}", response_model=CommissionDetailResponse)
async def get_commission_details(
    user_id: int,
    db: Session = Depends(get_db_session)
):
    """
    获取用户佣金详情
    
    Args:
        user_id: 用户ID
        db: 数据库会话
        
    Returns:
        CommissionDetailResponse: 详细佣金信息
    """
    try:
        # 验证用户存在
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 计算佣金
        commission_service = CommissionService(db)
        commission_data = commission_service.calculate_user_commission(user_id)
        
        # 构建响应
        balance = CommissionBalanceResponse(
            total_earnings=float(commission_data['total_earnings']),
            task_earnings=float(commission_data['task_earnings']),
            referral_earnings=float(commission_data['referral_earnings']),
            settled_amount=float(commission_data['settled_amount']),
            available_balance=float(commission_data['available_balance']),
            task_count=commission_data['task_count'],
            referral_task_count=commission_data['referral_task_count']
        )
        
        platform_details = []
        for detail in commission_data['platform_details']:
            platform_details.append(PlatformCommissionDetail(
                platform_code=detail['platform_code'],
                platform_name=detail['platform_name'],
                base_price=float(detail['base_price']),
                commission_rate=float(detail['commission_rate']),
                task_count=detail['task_count'],
                referral_task_count=detail['referral_task_count'],
                task_earnings=float(detail['task_earnings']),
                referral_earnings=float(detail['referral_earnings']),
                total_earnings=float(detail['total_earnings'])
            ))
        
        return CommissionDetailResponse(
            balance=balance,
            platform_details=platform_details
        )
        
    except Exception as e:
        logger.error(f"获取用户{user_id}佣金详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取佣金详情失败"
        )
