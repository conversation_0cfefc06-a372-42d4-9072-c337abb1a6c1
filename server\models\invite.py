"""
邀请码模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base


class InviteCode(Base):
    """邀请码表模型"""
    __tablename__ = "invite_codes"

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(32), unique=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 生成邀请码的用户
    used_by = Column(Integer, ForeignKey("users.id"))  # 使用邀请码的用户
    used_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())

    # 关系定义
    creator = relationship("User", foreign_keys=[user_id], back_populates="invite_codes")  # 创建者
    user = relationship("User", foreign_keys=[used_by], back_populates="used_invite_code")  # 使用者

    def __repr__(self):
        return f"<InviteCode(id={self.id}, code='{self.code}', used_by={self.used_by})>"
