"""
用户模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base
import enum


class UserStatus(enum.Enum):
    """用户状态枚举"""
    active = "active"
    disabled = "disabled"


class User(Base):
    """用户表模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    status = Column(Enum(UserStatus), default=UserStatus.active)
    referrer_id = Column(Integer, ForeignKey("users.id"))  # 推荐人ID
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    referrer = relationship("User", remote_side=[id], backref="referrals")  # 推荐人
    tasks = relationship("Task", back_populates="assigned_user")  # 分配的任务
    cookies = relationship("Cookie", back_populates="user")  # 用户的Cookie
    settlements = relationship("Settlement", back_populates="user")  # 结算记录
    invite_codes = relationship("InviteCode", foreign_keys="InviteCode.user_id", back_populates="creator")  # 创建的邀请码
    used_invite_code = relationship("InviteCode", foreign_keys="InviteCode.used_by", back_populates="user")  # 使用的邀请码

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', status='{self.status.value}')>"
