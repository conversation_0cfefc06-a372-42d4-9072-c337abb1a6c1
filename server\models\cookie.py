"""
Cookie模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, Enum, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base
import enum


class CookieStatus(enum.Enum):
    """Cookie状态枚举"""
    valid = "valid"
    invalid = "invalid"
    unknown = "unknown"


class Cookie(Base):
    """Cookie表模型"""
    __tablename__ = "cookies"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    platform_id = Column(Integer, ForeignKey("platforms.id"), nullable=False)
    cookie_data = Column(Text, nullable=False)  # 加密存储的Cookie数据
    status = Column(Enum(CookieStatus), default=CookieStatus.unknown)
    last_used_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    user = relationship("User", back_populates="cookies")
    platform = relationship("Platform", back_populates="cookies")

    def __repr__(self):
        return f"<Cookie(id={self.id}, user_id={self.user_id}, platform_id={self.platform_id}, status='{self.status.value}')>"
