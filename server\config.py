"""
应用配置管理
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Settings:
    """应用设置类"""
    
    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./ecommerce_crawler.db")
    
    # JWT认证配置
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    ALGORITHM: str = "HS256"
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # 安全配置
    BCRYPT_ROUNDS: int = int(os.getenv("BCRYPT_ROUNDS", "12"))
    

    
    @classmethod
    def validate_jwt_secret(cls) -> None:
        """验证JWT密钥安全性"""
        if cls.JWT_SECRET_KEY == "your-secret-key-change-in-production":
            if not cls.DEBUG:
                raise ValueError("生产环境必须设置安全的JWT_SECRET_KEY")
            print("警告: 使用默认JWT密钥，请在生产环境中更换")
        
        if len(cls.JWT_SECRET_KEY) < 32:
            raise ValueError("JWT_SECRET_KEY长度至少需要32个字符")
    
    @classmethod
    def get_database_url(cls) -> str:
        """获取数据库连接URL"""
        return cls.DATABASE_URL
    
    @classmethod
    def get_jwt_config(cls) -> dict:
        """获取JWT配置"""
        cls.validate_jwt_secret()
        return {
            "secret_key": cls.JWT_SECRET_KEY,
            "algorithm": cls.ALGORITHM,
            "expire_minutes": cls.ACCESS_TOKEN_EXPIRE_MINUTES
        }


# 全局设置实例
settings = Settings()

# 验证配置
try:
    settings.validate_jwt_secret()
except ValueError as e:
    if not settings.DEBUG:
        raise e
    print(f"配置警告: {e}")


def get_settings() -> Settings:
    """获取应用设置"""
    return settings
