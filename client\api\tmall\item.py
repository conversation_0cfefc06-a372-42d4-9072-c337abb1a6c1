import json
import time
import hashlib
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from curl_cffi import requests


def get_item_info(item_id, cookies):
    """
    ## 获取天猫商品详情
    - `item_id`: 商品id
    - `cookies`: dict, 必须包含 _m_h5_tk
    Returns:
        dict: {success: bool, data: dict|None, msg: str}
    """

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
        "referer": "https://item.taobao.com/",
    }
    if not item_id or not isinstance(cookies, dict):
        return {
            "success": False,
            "data": None,
            "msg": "参数不能为空，或 cookies 格式不正确",
        }
    try:
        # 原 _update_cookies 逻辑
        time_stamp = str(int(time.time() * 1000))
        params_dict = {
            "resId": "33799945",
            "bizId": "443",
            "fromUrl": "https://www.taobao.com/",
        }
        data_info_dict = {"params": json.dumps(params_dict, ensure_ascii=False)}
        data_info = json.dumps(data_info_dict, ensure_ascii=False)
        token = "undefined"
        update_url = "https://h5api.m.taobao.com/h5/mtop.tmall.kangaroo.core.service.route.aldlampservicefixedresv2/1.0/"
        update_params = {
            "appKey": "12574478",
            "t": time_stamp,
            "sign": hashlib.md5(
                (token + "&" + time_stamp + "&" + "12574478" + "&" + data_info).encode()
            ).hexdigest(),
            "data": data_info,
        }
        update_response = requests.get(
            update_url, headers=headers, params=update_params
        )
        cookie = update_response.cookies.get_dict()  # _m_h5_tk  _m_h5_tk_enc
        cookies.update(cookie)
    except Exception as e:
        return {
            "success": False,
            "data": None,
            "msg": f"更新 cookies 时发生错误: {str(e)}",
        }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
        "referer": "https://item.taobao.com/",
    }
    if not item_id or not isinstance(cookies, dict):
        return {
            "success": False,
            "data": None,
            "msg": "参数不能为空，或 cookies 格式不正确",
        }
    try:
        url = "https://h5api.m.taobao.com/h5/mtop.taobao.pcdetail.data.get/1.0/"
        time_stamp = str(int(time.time() * 1000))
        exParams = {
            "spm": "a21bo.jianhua/a.201876.d37.5af92a89I61ERe",
            "id": str(item_id),
            "scm": "1007.40986.369799.0",
            "pvid": "232c8d2b-d645-45d6-b9ad-60e321fbf8bc",
            "queryParams": f"id={item_id}&pvid=232c8d2b-d645-45d6-b9ad-60e321fbf8bc&scm=1007.40986.369799.0&spm=a21bo.jianhua%2Fa.201876.d37.5af92a89I61ERe",
            "domain": "https://item.taobao.com",
            "path_name": "/item.htm",
        }
        data_info_dict = {
            "id": str(item_id),
            "detail_v": "3.3.2",
            "exParams": json.dumps(exParams, ensure_ascii=False),
        }
        data_info = json.dumps(data_info_dict, ensure_ascii=False)
        token = cookies.get("_m_h5_tk", "").split("_")[0]
        params = {
            "appKey": "12574478",
            "t": time_stamp,
            "sign": hashlib.md5(
                (token + "&" + time_stamp + "&" + "12574478" + "&" + data_info).encode()
            ).hexdigest(),
            "ttid": "2022@taobao_litepc_9.17.0",
            "data": data_info,
        }
        response = requests.get(
            url, headers=headers, cookies=cookies, params=params, timeout=10
        )
        if response.status_code != 200:
            return {
                "success": False,
                "data": None,
                "msg": f"HTTP请求失败，状态码: {response.status_code}",
            }
        try:
            data = response.json()
        except Exception as e:
            return {
                "success": False,
                "data": None,
                "msg": f"响应JSON解析失败: {str(e)}",
            }
        if not data or "data" not in data:
            return {"success": False, "data": None, "msg": "未获取到商品信息"}
        return {"success": True, "data": data["data"], "msg": "获取商品信息成功"}
    except Exception as e:
        return {"success": False, "data": None, "msg": f"未知错误: {str(e)}"}

