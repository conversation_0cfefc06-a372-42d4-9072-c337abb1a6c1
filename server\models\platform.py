"""
平台模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Enum, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base
import enum


class PlatformStatus(enum.Enum):
    """平台状态枚举"""
    active = "active"
    disabled = "disabled"


class Platform(Base):
    """平台表模型"""
    __tablename__ = "platforms"

    id = Column(Integer, primary_key=True, autoincrement=True)
    code = Column(String(50), unique=True, nullable=False)  # 平台代码
    name = Column(String(100), nullable=False)  # 平台名称
    base_price = Column(DECIMAL(10, 2), nullable=False)  # 任务基准价格
    commission_rate = Column(DECIMAL(10, 2), nullable=False)  # 邀请佣金比例
    status = Column(Enum(PlatformStatus), default=PlatformStatus.active)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 关系定义
    tasks = relationship("Task", back_populates="platform")  # 平台的任务
    cookies = relationship("Cookie", back_populates="platform")  # 平台的Cookie

    def __repr__(self):
        return f"<Platform(id={self.id}, code='{self.code}', name='{self.name}')>"
